/**
 * v-permission指令功能测试
 * 测试regionPermissionKey和featurePermissionKey的支持
 */

import permissionManager from './index.js';

/**
 * 模拟Vue指令的binding对象
 */
function createBinding(value, modifiers = {}) {
    return {
        value,
        modifiers
    };
}

/**
 * 模拟DOM元素
 */
function createMockElement() {
    const element = {
        style: {},
        classList: {
            classes: [],
            add(className) {
                this.classes.push(className);
            }
        },
        parentNode: {
            removeChild: function(child) {
                console.log('Element removed from DOM');
                child._removed = true;
            }
        },
        disabled: false,
        _removed: false
    };
    return element;
}

/**
 * 模拟权限管理器的checkPermission方法
 */
function mockPermissionManager() {
    const originalCheckPermission = permissionManager.checkPermission;
    const originalCheckRegionPermission = permissionManager.checkRegionPermission;
    const originalCheckFeaturePermission = permissionManager.checkFeaturePermission;

    // 模拟权限检查结果
    permissionManager.checkPermission = function(permission, context = {}) {
        console.log('checkPermission called with:', permission, context);
        
        if (typeof permission === 'string') {
            return originalCheckPermission.call(this, permission, context);
        }
        
        const { regionPermissionKey, featurePermissionKey } = permission;
        
        // 模拟组合权限检查逻辑
        if (regionPermissionKey && featurePermissionKey) {
            console.log(`Checking combined permissions: region=${regionPermissionKey}, feature=${featurePermissionKey}`);
            return regionPermissionKey === 'live' && featurePermissionKey === 'conference_management';
        } else if (regionPermissionKey) {
            console.log(`Checking region permission: ${regionPermissionKey}`);
            return regionPermissionKey === 'live' || regionPermissionKey === 'breastAI';
        } else if (featurePermissionKey) {
            console.log(`Checking feature permission: ${featurePermissionKey}`);
            return featurePermissionKey === 'user_management';
        }
        
        return false;
    };

    permissionManager.checkRegionPermission = function(permission) {
        console.log('checkRegionPermission called with:', permission);
        return permission === 'live' || permission === 'breastAI';
    };

    permissionManager.checkFeaturePermission = function(permission) {
        console.log('checkFeaturePermission called with:', permission);
        return permission === 'user_management';
    };

    return {
        restore() {
            permissionManager.checkPermission = originalCheckPermission;
            permissionManager.checkRegionPermission = originalCheckRegionPermission;
            permissionManager.checkFeaturePermission = originalCheckFeaturePermission;
        }
    };
}

/**
 * 测试v-permission指令的新功能
 */
function testVPermissionDirective() {
    console.log('=== 开始测试 v-permission 指令新功能 ===\n');

    const mock = mockPermissionManager();

    try {
        // 获取指令定义（模拟Vue.directive的注册）
        const directive = {
            bind(el, binding, vnode) {
                const { value, modifiers } = binding;

                if (!value) {
                    console.warn('v-permission directive requires a value');
                    return;
                }

                let hasPermission = false;

                if (typeof value === 'string') {
                    // 简单权限检查
                    if (modifiers.route) {
                        hasPermission = permissionManager.checkRoutePermission(value);
                    } else if (modifiers.component) {
                        hasPermission = permissionManager.checkComponentPermission(value);
                    } else if (modifiers.feature) {
                        hasPermission = permissionManager.checkFeaturePermission(value);
                    } else if (modifiers.region) {
                        hasPermission = permissionManager.checkRegionPermission(value);
                    } else {
                        // 默认检查通用权限（包括区域权限）
                        hasPermission = permissionManager.checkPermission(value);
                    }
                } else if (typeof value === 'object') {
                    // 复杂权限检查
                    const { type, permission, action, context, regionPermissionKey, featurePermissionKey } = value;

                    // 如果提供了regionPermissionKey或featurePermissionKey，使用通用权限检查
                    if (regionPermissionKey || featurePermissionKey) {
                        hasPermission = permissionManager.checkPermission({
                            regionPermissionKey,
                            featurePermissionKey
                        }, context || {});
                    } else {
                        // 原有的类型化权限检查
                        switch (type) {
                        case 'route':
                            hasPermission = permissionManager.checkRoutePermission(permission, context);
                            break;
                        case 'component':
                            hasPermission = permissionManager.checkComponentPermission(permission, action, context);
                            break;
                        case 'feature':
                            hasPermission = permissionManager.checkFeaturePermission(permission, action, context);
                            break;
                        case 'api':
                            hasPermission = permissionManager.checkApiPermission(permission, action, context);
                            break;
                        case 'data':
                            hasPermission = permissionManager.checkDataPermission(permission, action, context);
                            break;
                        default:
                            hasPermission = permissionManager.checkFeaturePermission(permission, action, context);
                        }
                    }
                }

                // 根据权限结果处理元素
                if (!hasPermission) {
                    if (modifiers.hide) {
                        // 隐藏元素
                        el.style.display = 'none';
                    } else if (modifiers.disable) {
                        // 禁用元素
                        el.disabled = true;
                        el.classList.add('disabled');
                    } else {
                        // 默认移除元素
                        el.parentNode && el.parentNode.removeChild(el);
                    }
                }

                return hasPermission;
            }
        };

        // 测试用例
        const testCases = [
            {
                name: '测试1: 只检查区域权限 (regionPermissionKey)',
                binding: createBinding({
                    regionPermissionKey: 'live'
                }),
                expectedResult: true
            },
            {
                name: '测试2: 只检查功能权限 (featurePermissionKey)',
                binding: createBinding({
                    featurePermissionKey: 'user_management'
                }),
                expectedResult: true
            },
            {
                name: '测试3: 组合权限检查 (都有权限)',
                binding: createBinding({
                    regionPermissionKey: 'live',
                    featurePermissionKey: 'conference_management'
                }),
                expectedResult: true
            },
            {
                name: '测试4: 组合权限检查 (区域权限不足)',
                binding: createBinding({
                    regionPermissionKey: 'tvwall',
                    featurePermissionKey: 'conference_management'
                }),
                expectedResult: false
            },
            {
                name: '测试5: 区域权限不足',
                binding: createBinding({
                    regionPermissionKey: 'qcStatistics'
                }),
                expectedResult: false
            },
            {
                name: '测试6: 功能权限不足',
                binding: createBinding({
                    featurePermissionKey: 'admin_access'
                }),
                expectedResult: false
            },
            {
                name: '测试7: 隐藏模式 (无权限)',
                binding: createBinding({
                    regionPermissionKey: 'qcStatistics'
                }, { hide: true }),
                expectedResult: false
            },
            {
                name: '测试8: 禁用模式 (无权限)',
                binding: createBinding({
                    regionPermissionKey: 'qcStatistics'
                }, { disable: true }),
                expectedResult: false
            }
        ];

        // 执行测试
        testCases.forEach((testCase, index) => {
            console.log(`\n${testCase.name}`);
            console.log('输入:', JSON.stringify(testCase.binding.value));
            console.log('修饰符:', JSON.stringify(testCase.binding.modifiers));

            const element = createMockElement();
            const result = directive.bind(element, testCase.binding);

            console.log('权限检查结果:', result);
            console.log('预期结果:', testCase.expectedResult);
            console.log('测试结果:', result === testCase.expectedResult ? '✓ 通过' : '✗ 失败');

            // 检查元素状态
            if (!result) {
                if (testCase.binding.modifiers.hide) {
                    console.log('元素状态: 已隐藏 (display: none)');
                } else if (testCase.binding.modifiers.disable) {
                    console.log('元素状态: 已禁用 (disabled + class)');
                } else {
                    console.log('元素状态: 已移除');
                }
            } else {
                console.log('元素状态: 正常显示');
            }
        });

        console.log('\n=== 测试完成 ===');

    } finally {
        mock.restore();
    }
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testVPermissionDirective,
        createBinding,
        createMockElement
    };
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.testVPermissionDirective = testVPermissionDirective;
}

// 自动运行测试（如果直接执行此文件）
if (typeof require !== 'undefined' && require.main === module) {
    testVPermissionDirective();
}
