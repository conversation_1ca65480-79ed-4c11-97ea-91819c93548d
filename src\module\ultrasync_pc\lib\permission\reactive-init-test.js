/**
 * 测试权限管理器响应式初始化状态
 * 验证Vue组件能够在权限管理器初始化后自动更新
 */

import permissionManager from './index.js';

/**
 * 模拟Vue组件的计算属性
 */
class MockVueComponent {
    constructor() {
        // 模拟Vue的响应式数据
        this.data = {
            $permissionInitialized: permissionManager.isInitialized(),
            $regionPermissionInitialized: permissionManager.regionInitialized
        };
        
        // 模拟计算属性的依赖追踪
        this.computedCache = {};
        this.computedDeps = {};
        
        // 监听权限管理器初始化事件
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        const updateInitStatus = () => {
            console.log('权限初始化状态更新');
            this.data.$permissionInitialized = permissionManager.isInitialized();
            this.data.$regionPermissionInitialized = permissionManager.regionInitialized;
            
            // 清除计算属性缓存，模拟Vue的响应式更新
            this.computedCache = {};
            
            // 重新计算依赖的计算属性
            console.log('重新计算 filteredSubmenuItems:', this.filteredSubmenuItems);
        };
        
        window.addEventListener('permission:initialized', updateInitStatus);
        window.addEventListener('permission:regionInitialized', updateInitStatus);
    }
    
    // 模拟Vue混入的方法
    $isPermissionInitialized() {
        return this.data.$permissionInitialized;
    }
    
    $isRegionPermissionInitialized() {
        return this.data.$regionPermissionInitialized;
    }
    
    $checkPermission(permission) {
        return permissionManager.checkPermission(permission);
    }
    
    // 模拟leftSideBar.vue的计算属性
    get filteredSubmenuItems() {
        if (this.computedCache.filteredSubmenuItems) {
            return this.computedCache.filteredSubmenuItems;
        }
        
        const submenuItems = [
            {
                name: 'system_setting',
                label: '系统设置',
                icon: 'el-icon-setting',
                action: 'openSystemSetting'
            },
            {
                name: 'background_manage',
                label: '后台管理',
                icon: 'el-icon-s-tools',
                action: 'openBackgroundManage'
            }
        ];
        
        const result = submenuItems.filter(item => {
            // 后台管理只对管理员显示
            if (item.name === 'background_manage') {
                // 依赖响应式的初始化状态，确保权限管理器初始化后重新计算
                if (!this.$isPermissionInitialized()) {
                    console.warn('PermissionManager not initialized yet, hiding background_manage');
                    return false;
                }
                console.log('backgroundManage permission check:', this.$checkPermission('backgroundManage'));
                return this.$checkPermission('backgroundManage');
            }
            // 其他菜单项都显示
            return true;
        });
        
        this.computedCache.filteredSubmenuItems = result;
        return result;
    }
}

/**
 * 测试响应式初始化状态
 */
async function testReactiveInitialization() {
    console.log('=== 测试权限管理器响应式初始化状态 ===\n');
    
    // 创建模拟组件
    const component = new MockVueComponent();
    
    console.log('1. 初始状态检查');
    console.log('权限管理器初始化状态:', component.$isPermissionInitialized());
    console.log('区域权限初始化状态:', component.$isRegionPermissionInitialized());
    console.log('初始 filteredSubmenuItems:', component.filteredSubmenuItems.map(item => item.name));
    
    console.log('\n2. 初始化区域权限');
    try {
        await permissionManager.initializeRegionPermissions({
            functionsStatus: {
                live: 1,
                library: 1,
                backgroundManage: 1
            }
        });
        console.log('区域权限初始化完成');
    } catch (error) {
        console.error('区域权限初始化失败:', error);
    }
    
    // 等待事件处理
    await new Promise(resolve => setTimeout(resolve, 100));
    
    console.log('\n3. 区域权限初始化后状态检查');
    console.log('权限管理器初始化状态:', component.$isPermissionInitialized());
    console.log('区域权限初始化状态:', component.$isRegionPermissionInitialized());
    console.log('区域权限初始化后 filteredSubmenuItems:', component.filteredSubmenuItems.map(item => item.name));
    
    console.log('\n4. 初始化用户权限');
    try {
        await permissionManager.initialize({
            uid: 'test-user',
            role: 3, // 管理员角色
            type: 1,
            nickname: '测试管理员'
        });
        console.log('用户权限初始化完成');
    } catch (error) {
        console.error('用户权限初始化失败:', error);
    }
    
    // 等待事件处理
    await new Promise(resolve => setTimeout(resolve, 100));
    
    console.log('\n5. 用户权限初始化后状态检查');
    console.log('权限管理器初始化状态:', component.$isPermissionInitialized());
    console.log('区域权限初始化状态:', component.$isRegionPermissionInitialized());
    console.log('用户权限初始化后 filteredSubmenuItems:', component.filteredSubmenuItems.map(item => item.name));
    
    console.log('\n6. 验证权限检查');
    console.log('backgroundManage 权限:', component.$checkPermission('backgroundManage'));
    console.log('最终 filteredSubmenuItems:', component.filteredSubmenuItems.map(item => item.name));
    
    console.log('\n=== 测试完成 ===');
    
    // 验证结果
    const finalItems = component.filteredSubmenuItems;
    const hasBackgroundManage = finalItems.some(item => item.name === 'background_manage');
    
    if (hasBackgroundManage) {
        console.log('✓ 成功：权限管理器初始化后，后台管理菜单正确显示');
    } else {
        console.log('✗ 失败：权限管理器初始化后，后台管理菜单仍然隐藏');
    }
    
    return hasBackgroundManage;
}

/**
 * 模拟浏览器环境
 */
function setupMockEnvironment() {
    // 模拟 window.vm 和 $store
    window.vm = {
        $store: {
            state: {
                globalParams: {
                    region: 'CN',
                    isCE: false,
                    functionsStatus: {
                        live: 1,
                        library: 1,
                        backgroundManage: 1
                    }
                }
            }
        },
        $emit: function(eventName, data) {
            console.log(`Vue事件触发: ${eventName}`, data);
            // 触发自定义事件
            const event = new CustomEvent(eventName, { detail: data });
            window.dispatchEvent(event);
        }
    };
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testReactiveInitialization,
        MockVueComponent,
        setupMockEnvironment
    };
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.testReactiveInitialization = testReactiveInitialization;
    
    // 自动运行测试
    if (typeof require === 'undefined') {
        setupMockEnvironment();
        testReactiveInitialization().catch(console.error);
    }
}

// 自动运行测试（如果直接执行此文件）
if (typeof require !== 'undefined' && require.main === module) {
    setupMockEnvironment();
    testReactiveInitialization().catch(console.error);
}
