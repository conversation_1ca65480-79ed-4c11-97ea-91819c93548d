/**
 * 权限更新工具
 * 提供优雅的权限实时更新机制
 */

import permissionManager from './index.js';

/**
 * 权限更新器类
 */
class PermissionUpdater {
    constructor() {
        this.updateQueue = [];
        this.isUpdating = false;
        this.updateDelay = 100; // 防抖延迟
    }

    /**
     * 更新用户权限信息
     * @param {Object} newUserInfo - 新的用户信息
     * @param {Object} options - 更新选项
     */
    async updateUserPermissions(newUserInfo, options = {}) {
        const {
            showMessage = false,
            messageType = 'info',
            customMessage = null,
            forceUpdate = false
        } = options;

        try {
            console.log('[PermissionUpdater] Updating user permissions:', newUserInfo);

            // 获取旧的用户信息
            const oldUserInfo = permissionManager.getUserInfo();
            const oldRole = permissionManager.getUserRole();
            const oldUserId = permissionManager.getUserId();

            // 更新权限管理器
            permissionManager.updateUserInfo(newUserInfo);

            // 获取新的用户信息
            const newRole = permissionManager.getUserRole();
            const newUserId = permissionManager.getUserId();

            // 检查是否有实际变化
            const hasRoleChanged = oldRole !== newRole;
            const hasUserChanged = oldUserId !== newUserId;

            if (hasRoleChanged || hasUserChanged || forceUpdate) {
                // 显示更新消息
                if (showMessage && typeof window !== 'undefined' && window.vm && window.vm.$message) {
                    const message = customMessage || this.getUpdateMessage(oldRole, newRole, hasRoleChanged, hasUserChanged);
                    window.vm.$message[messageType](message);
                }

                // 触发页面更新
                await this.triggerPageUpdate();

                console.log('[PermissionUpdater] Permission update completed');
                return {
                    success: true,
                    hasRoleChanged,
                    hasUserChanged,
                    oldRole,
                    newRole,
                    oldUserId,
                    newUserId
                };
            } else {
                console.log('[PermissionUpdater] No permission changes detected');
                return {
                    success: true,
                    hasRoleChanged: false,
                    hasUserChanged: false
                };
            }
        } catch (error) {
            console.error('[PermissionUpdater] Error updating permissions:', error);
            if (showMessage && typeof window !== 'undefined' && window.vm && window.vm.$message) {
                window.vm.$message.error('权限更新失败，请刷新页面重试');
            }
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 批量更新多个组件的权限
     * @param {Array} components - 组件实例数组
     */
    async batchUpdateComponents(components) {
        if (!Array.isArray(components)) {
            components = [components];
        }

        for (const component of components) {
            if (component && typeof component.$forceUpdate === 'function') {
                component.$forceUpdate();
            }
        }
    }

    /**
     * 触发页面更新
     */
    async triggerPageUpdate() {
        // 防抖处理
        if (this.isUpdating) {
            return;
        }

        this.isUpdating = true;

        try {
            // 等待一个事件循环，确保所有同步更新完成
            await new Promise(resolve => setTimeout(resolve, this.updateDelay));

            // 触发全局更新事件
            if (typeof window !== 'undefined') {
                const event = new CustomEvent('permission:forceUpdate', {
                    detail: { timestamp: Date.now() }
                });
                window.dispatchEvent(event);
            }

            // 如果有Vue实例，触发全局更新
            if (typeof window !== 'undefined' && window.vm) {
                if (window.vm.$emit) {
                    window.vm.$emit('permission:forceUpdate');
                }
                if (window.vm.$root && window.vm.$root.eventBus && window.vm.$root.eventBus.$emit) {
                    window.vm.$root.eventBus.$emit('permission:forceUpdate');
                }
            }
        } finally {
            this.isUpdating = false;
        }
    }

    /**
     * 获取更新消息
     */
    getUpdateMessage(oldRole, newRole, hasRoleChanged, hasUserChanged) {
        if (hasRoleChanged) {
            const roleNames = {
                0: '临时用户',
                1: '普通用户',
                2: '管理员',
                3: '超级管理员',
                4: '主任'
            };
            const oldRoleName = roleNames[oldRole] || `角色${oldRole}`;
            const newRoleName = roleNames[newRole] || `角色${newRole}`;
            return `2用户角色已更新: ${oldRoleName} → ${newRoleName}`;
        }

        if (hasUserChanged) {
            return '用户信息已更新';
        }

        return '权限信息已更新';
    }

    /**
     * 重置权限状态
     */
    async resetPermissions() {
        try {
            permissionManager.clearCache();
            await permissionManager.loadPermissions();
            await this.triggerPageUpdate();

            console.log('[PermissionUpdater] Permissions reset completed');
            return { success: true };
        } catch (error) {
            console.error('[PermissionUpdater] Error resetting permissions:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 检查权限是否需要更新
     * @param {Object} newUserInfo - 新的用户信息
     * @returns {boolean} 是否需要更新
     */
    needsUpdate(newUserInfo) {
        const currentUserInfo = permissionManager.getUserInfo();

        if (!currentUserInfo) {
            return true;
        }

        // 检查关键字段是否发生变化
        const keyFields = ['role', 'uid', 'id', 'type', 'permissions'];

        for (const field of keyFields) {
            if (newUserInfo[field] !== currentUserInfo[field]) {
                return true;
            }
        }

        return false;
    }
}

// 创建单例实例
const permissionUpdater = new PermissionUpdater();

// 导出实例和类
export default permissionUpdater;
export { PermissionUpdater };

// 便捷方法导出
export const {
    updateUserPermissions,
    batchUpdateComponents,
    triggerPageUpdate,
    resetPermissions,
    needsUpdate
} = permissionUpdater;
