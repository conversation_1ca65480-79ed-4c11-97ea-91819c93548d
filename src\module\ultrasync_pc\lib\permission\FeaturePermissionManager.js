import BasePermissionManager from './BasePermissionManager.js';
import { USER_ROLE } from '../constants.js';
/**
 * 功能权限管理器
 * 负责具体功能操作的权限控制
 */
class FeaturePermissionManager extends BasePermissionManager {
    constructor() {
        super();
        this.featurePermissions = new Map(); // 功能权限配置
        this.apiPermissions = new Map(); // API权限配置
        this.dataPermissions = new Map(); // 数据权限配置
    }

    /**
     * 加载权限数据
     */
    async loadPermissions() {
        this.loadFeaturePermissions();
        this.loadApiPermissions();
        this.loadDataPermissions();
    }

    /**
     * 加载功能权限配置
     */
    loadFeaturePermissions() {
        const featurePermissions = {
            // 后台管理功能
            'backgroundManage': {
                'default': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: [] },
            },
            // 电视墙功能
            'tvWall': {
                'default': { roles: [USER_ROLE.DIRECTOR, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], permissions: ['tvWall'] },
            },
        };

        Object.entries(featurePermissions).forEach(([feature, permissions]) => {
            this.featurePermissions.set(feature, permissions);
        });
    }

    /**
     * 加载API权限配置
     */
    loadApiPermissions() {
        const apiPermissions = {
            // 用户相关API
            'POST /api/user/create': { roles: [2, 3, 5], permissions: ['user_create'] },
            'PUT /api/user/update': { roles: [2, 3, 5], permissions: ['user_edit'] },
            'DELETE /api/user/delete': { roles: [3, 5], permissions: ['user_delete'] },
            'GET /api/user/list': { roles: [1, 2, 3, 4, 5, 6], permissions: ['user_view'] },
            'POST /api/user/role': { roles: [3, 5], permissions: ['user_role_change'] },

            // 群组相关API
            'POST /api/group/create': { roles: [2, 3, 5], permissions: ['group_create'] },
            'PUT /api/group/update': { roles: [2, 3, 5], permissions: ['group_edit'] },
            'DELETE /api/group/delete': { roles: [3, 5], permissions: ['group_delete'] },
            'POST /api/group/members': { roles: [2, 3, 5], permissions: ['group_member_manage'] },

            // 多中心相关API
            'POST /api/multicenter/exam': { roles: [1, 2, 3, 4, 5, 6], permissions: ['exam_create'] },
            'PUT /api/multicenter/exam': { roles: [1, 2, 3, 4, 5, 6], permissions: ['exam_edit'] },
            'DELETE /api/multicenter/exam': { roles: [2, 3, 5], permissions: ['exam_delete'] },
            'POST /api/multicenter/assign': { roles: [2, 3, 5], permissions: ['exam_assign'] },
            'POST /api/multicenter/review': { roles: [3, 4], permissions: ['exam_review'] },

            // 文件相关API
            'POST /api/file/upload': { roles: [1, 2, 3, 4, 5, 6], permissions: ['file_upload'] },
            'GET /api/file/download': { roles: [1, 2, 3, 4, 5, 6], permissions: ['file_download'] },
            'DELETE /api/file/delete': { roles: [2, 3, 5], permissions: ['file_delete'] },
            'POST /api/data/export': { roles: [2, 3, 5], permissions: ['data_export'] }
        };

        Object.entries(apiPermissions).forEach(([api, config]) => {
            this.apiPermissions.set(api, config);
        });
    }

    /**
     * 加载数据权限配置
     */
    loadDataPermissions() {
        const dataPermissions = {
            // 用户数据权限
            'user_data': {
                'own_data': { roles: [1, 2, 3, 4, 5, 6], permissions: ['own_data_access'] },
                'department_data': { roles: [2, 3, 5], permissions: ['department_data_access'] },
                'all_data': { roles: [5], permissions: ['all_data_access'] }
            },

            // 群组数据权限
            'group_data': {
                'member_data': { roles: [1, 2, 3, 4, 5, 6], permissions: ['group_member_data'] },
                'manager_data': { roles: [2, 3, 5], permissions: ['group_manager_data'] },
                'admin_data': { roles: [3, 5], permissions: ['group_admin_data'] }
            },

            // 多中心数据权限
            'multicenter_data': {
                'own_exams': { roles: [1, 2, 3, 4, 5, 6], permissions: ['own_exam_data'] },
                'assigned_exams': { roles: [2, 3, 4], permissions: ['assigned_exam_data'] },
                'all_exams': { roles: [5], permissions: ['all_exam_data'] },
                'statistics_data': { roles: [2, 3, 5], permissions: ['statistics_data'] }
            }
        };

        Object.entries(dataPermissions).forEach(([dataType, permissions]) => {
            this.dataPermissions.set(dataType, permissions);
        });
    }

    /**
     * 检查功能权限
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    hasPermission(feature, action = null, context = {}) {
        if (!this.isInitialized()) {
            console.warn('FeaturePermissionManager not initialized');
            return false;
        }

        // 如果只传入功能名，检查功能的基本访问权限
        if (!action) {
            return this.checkFeatureAccess(feature, context);
        }

        // 检查功能的特定操作权限
        return this.checkFeatureActionPermission(feature, action, context);
    }

    /**
     * 检查功能访问权限
     * @param {string} feature - 功能名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkFeatureAccess(feature, context = {}) {
        const featureConfig = this.featurePermissions.get(feature);

        if (!featureConfig) {
            return true; // 没有配置默认允许
        }
        // 检查是否有任何一个操作的权限
        for (let [action, config] of Object.entries(featureConfig)) {
            if (this.checkPermissionConfig(config, context)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查功能操作权限
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkFeatureActionPermission(feature, action, context = {}) {
        const featureConfig = this.featurePermissions.get(feature);

        if (!featureConfig) {
            return true; // 没有配置默认允许
        }

        const actionConfig = featureConfig[action];
        if (!actionConfig) {
            return true; // 没有配置默认允许
        }

        return this.checkPermissionConfig(actionConfig, context);
    }

    /**
     * 检查API权限
     * @param {string} method - HTTP方法
     * @param {string} path - API路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkApiPermission(method, path, context = {}) {
        const apiKey = `${method.toUpperCase()} ${path}`;
        const apiConfig = this.apiPermissions.get(apiKey);

        if (!apiConfig) {
            return true; // 没有配置默认允许
        }

        return this.checkPermissionConfig(apiConfig, context);
    }

    /**
     * 检查数据权限
     * @param {string} dataType - 数据类型
     * @param {string} scope - 数据范围
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkDataPermission(dataType, scope, context = {}) {
        const dataConfig = this.dataPermissions.get(dataType);

        if (!dataConfig) {
            return true; // 没有配置默认允许
        }

        const scopeConfig = dataConfig[scope];
        if (!scopeConfig) {
            return true; // 没有配置默认允许
        }

        return this.checkPermissionConfig(scopeConfig, context);
    }

    /**
     * 检查权限配置
     * @param {Object} config - 权限配置
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkPermissionConfig(config, context = {}) {
        // 检查角色权限
        if (config.roles && config.roles.length > 0) {
            const userRole = this.getUserRole();
            if (!config.roles.includes(userRole)) {
                return false;
            }
        }

        //检查特定权限
        if (config.permissions && config.permissions.length > 0) {
            return config.permissions.every(permission =>
                this.checkSpecificPermission(permission, context)
            );
        }

        return true;
    }

    /**
     * 检查特定权限
     * @param {string} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkSpecificPermission(permission, context = {}) {
        const userId = this.getUserId();
        const userRole = this.getUserRole();

        // 预定义的权限映射表
        const permissionMap = {
            // === 基础权限 ===
            'user': () => userId !== null && userId !== undefined,
            'admin': () => this.isAdmin(),
            'super_admin': () => this.isSuperAdmin(),
            'system_config': () => userRole >= USER_ROLE.SUPER_ADMIN,
            'user_management': () => userRole >= USER_ROLE.ADMIN,
            'role_management': () => userRole >= USER_ROLE.SUPER_ADMIN,

            // === 电视墙权限 ===
            'tvWall': () => userRole >= USER_ROLE.DIRECTOR,
            'tvWall_control': () => userRole >= USER_ROLE.ADMIN,
            'tvWall_config': () => userRole >= USER_ROLE.SUPER_ADMIN,

            // === 用户操作权限 ===
            'user_create': () => userRole >= USER_ROLE.ADMIN,
            'user_edit': () => this.checkUserEditPermission(context),
            'user_delete': () => userRole >= USER_ROLE.SUPER_ADMIN,
            'user_view': () => userRole >= USER_ROLE.NORMAL_USER,
            'user_role_change': () => userRole >= USER_ROLE.SUPER_ADMIN,

            // === 群组操作权限 ===
            'group_create': () => userRole >= USER_ROLE.ADMIN,
            'group_edit': () => this.checkGroupEditPermission(context),
            'group_delete': () => userRole >= USER_ROLE.SUPER_ADMIN,
            'group_member_manage': () => this.checkGroupMemberManagePermission(context),

            // === 多中心权限 ===
            'exam_create': () => userRole >= USER_ROLE.NORMAL_USER,
            'exam_edit': () => this.checkExamEditPermission(context),
            'exam_delete': () => this.checkExamDeletePermission(context),
            'exam_assign': () => userRole >= USER_ROLE.ADMIN,
            'exam_review': () => this.checkExamReviewPermission(context),
            'multicenter_access': () => this.checkMulticenterAccess(context),

            // === 文件操作权限 ===
            'file_upload': () => userRole >= USER_ROLE.NORMAL_USER,
            'file_download': () => userRole >= USER_ROLE.NORMAL_USER,
            'file_delete': () => this.checkFileDeletePermission(context),
            'data_export': () => userRole >= USER_ROLE.ADMIN,

            // === 数据权限 ===
            'own_data_access': () => userRole >= USER_ROLE.NORMAL_USER,
            'department_data_access': () => this.checkDepartmentDataAccess(context),
            'all_data_access': () => userRole >= USER_ROLE.SUPER_ADMIN,

            // === 培训相关权限 ===
            'training_manage': () => userRole >= USER_ROLE.ADMIN,
            'training_assign': () => userRole >= USER_ROLE.ADMIN,
            'training_review': () => this.checkTrainingReviewPermission(context),
            'training_statistics': () => userRole >= USER_ROLE.ADMIN,
        };

        // 检查预定义权限
        if (permissionMap[permission]) {
            try {
                return permissionMap[permission]();
            } catch (error) {
                console.error(`Error checking permission ${permission}:`, error);
                return false;
            }
        }

        // 未定义的权限默认拒绝（安全策略）
        console.warn(`Unknown permission: ${permission}`);
        return false;
    }

    /**
     * 获取用户可执行的功能操作
     * @param {string} feature - 功能名称
     * @returns {Array<string>} 可执行的操作列表
     */
    getAvailableActions(feature) {
        const featureConfig = this.featurePermissions.get(feature);

        if (!featureConfig) {
            return [];
        }

        const availableActions = [];
        for (let [action, config] of Object.entries(featureConfig)) {
            if (this.checkPermissionConfig(config)) {
                availableActions.push(action);
            }
        }

        return availableActions;
    }

    /**
     * 批量检查功能权限
     * @param {Array} features - 功能配置数组 [{feature, action, context}]
     * @returns {Object} 权限检查结果
     */
    batchCheckPermissions(features) {
        const results = {};

        features.forEach(({ feature, action, context = {}, key }) => {
            const permissionKey = key || `${feature}${action ? '_' + action : ''}`;
            results[permissionKey] = this.hasPermission(feature, action, context);
        });

        return results;
    }

    /**
     * 添加功能权限配置
     * @param {string} feature - 功能名称
     * @param {Object} permissions - 权限配置
     */
    addFeaturePermission(feature, permissions) {
        this.featurePermissions.set(feature, permissions);
    }

    /**
     * 添加API权限配置
     * @param {string} api - API标识
     * @param {Object} config - 权限配置
     */
    addApiPermission(api, config) {
        this.apiPermissions.set(api, config);
    }

    /**
     * 添加数据权限配置
     * @param {string} dataType - 数据类型
     * @param {Object} permissions - 权限配置
     */
    addDataPermission(dataType, permissions) {
        this.dataPermissions.set(dataType, permissions);
    }

    // === 具体权限检查方法 ===

    /**
     * 检查用户编辑权限
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkUserEditPermission(context = {}) {
        const userRole = this.getUserRole();
        const userId = this.getUserId();
        const targetUserId = context.targetUserId;

        // 超级管理员和管理员可以编辑所有用户
        if (userRole >= USER_ROLE.ADMIN) {
            return true;
        }

        // 用户只能编辑自己的信息
        if (targetUserId && userId === targetUserId) {
            return true;
        }

        return false;
    }

    /**
     * 检查群组编辑权限
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkGroupEditPermission(context = {}) {
        const userRole = this.getUserRole();
        const userId = this.getUserId();
        const groupOwnerId = context.groupOwnerId;

        // 管理员可以编辑所有群组
        if (userRole >= USER_ROLE.ADMIN) {
            return true;
        }

        // 群组创建者可以编辑自己的群组
        if (groupOwnerId && userId === groupOwnerId) {
            return true;
        }

        return false;
    }

    /**
     * 检查群组成员管理权限
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkGroupMemberManagePermission(context = {}) {
        const userRole = this.getUserRole();
        const userId = this.getUserId();
        const groupOwnerId = context.groupOwnerId;
        const isGroupManager = context.isGroupManager;

        // 管理员可以管理所有群组成员
        if (userRole >= USER_ROLE.ADMIN) {
            return true;
        }

        // 群组创建者可以管理成员
        if (groupOwnerId && userId === groupOwnerId) {
            return true;
        }

        // 群组管理员可以管理成员
        if (isGroupManager) {
            return true;
        }

        return false;
    }

    /**
     * 检查考试编辑权限
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkExamEditPermission(context = {}) {
        const userRole = this.getUserRole();
        const userId = this.getUserId();
        const examCreatorId = context.examCreatorId;
        const examStatus = context.examStatus;

        // 管理员可以编辑所有考试
        if (userRole >= USER_ROLE.ADMIN) {
            return true;
        }

        // 考试创建者可以编辑自己的考试
        if (examCreatorId && userId === examCreatorId) {
            // 如果考试已经开始或结束，则不能编辑
            if (examStatus === 'started' || examStatus === 'finished') {
                return false;
            }
            return true;
        }

        return false;
    }

    /**
     * 检查考试删除权限
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkExamDeletePermission(context = {}) {
        const userRole = this.getUserRole();
        const userId = this.getUserId();
        const examCreatorId = context.examCreatorId;
        const examStatus = context.examStatus;
        const hasParticipants = context.hasParticipants;

        // 只有管理员可以删除考试
        if (userRole >= USER_ROLE.ADMIN) {
            // 如果考试已有参与者，需要超级管理员权限
            if (hasParticipants && userRole < USER_ROLE.SUPER_ADMIN) {
                return false;
            }
            return true;
        }

        // 考试创建者可以删除自己创建的考试（仅在未开始且无参与者时）
        if (examCreatorId && userId === examCreatorId) {
            if (examStatus === 'draft' && !hasParticipants) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查考试评审权限
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkExamReviewPermission(context = {}) {
        const userRole = this.getUserRole();
        const userId = this.getUserId();
        const assignedReviewerId = context.assignedReviewerId;

        // 主任和管理员可以评审
        if (userRole >= USER_ROLE.DIRECTOR) {
            return true;
        }

        // 指定的评审员可以评审
        if (assignedReviewerId && userId === assignedReviewerId) {
            return true;
        }

        return false;
    }

    /**
     * 检查多中心访问权限
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkMulticenterAccess(context = {}) {
        const userRole = this.getUserRole();
        const centerId = context.centerId;
        const userCenterIds = context.userCenterIds || [];

        // 管理员可以访问所有中心
        if (userRole >= USER_ROLE.ADMIN) {
            return true;
        }

        // 检查用户是否属于该中心
        if (centerId && userCenterIds.includes(centerId)) {
            return true;
        }

        // 普通用户至少需要属于一个中心
        return userCenterIds.length > 0;
    }

    /**
     * 检查文件删除权限
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkFileDeletePermission(context = {}) {
        const userRole = this.getUserRole();
        const userId = this.getUserId();
        const fileOwnerId = context.fileOwnerId;
        const fileType = context.fileType;

        // 管理员可以删除所有文件
        if (userRole >= USER_ROLE.ADMIN) {
            return true;
        }

        // 用户可以删除自己上传的文件
        if (fileOwnerId && userId === fileOwnerId) {
            // 某些重要文件类型需要管理员权限
            const restrictedTypes = ['system_config', 'exam_template', 'training_material'];
            if (restrictedTypes.includes(fileType)) {
                return false;
            }
            return true;
        }

        return false;
    }

    /**
     * 检查部门数据访问权限
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkDepartmentDataAccess(context = {}) {
        const userRole = this.getUserRole();
        const userDepartmentId = context.userDepartmentId;
        const targetDepartmentId = context.targetDepartmentId;

        // 管理员可以访问所有部门数据
        if (userRole >= USER_ROLE.ADMIN) {
            return true;
        }

        // 用户可以访问自己部门的数据
        if (userDepartmentId && targetDepartmentId && userDepartmentId === targetDepartmentId) {
            return true;
        }

        return false;
    }

    /**
     * 检查培训评审权限
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkTrainingReviewPermission(context = {}) {
        const userRole = this.getUserRole();
        const userId = this.getUserId();
        const assignedSupervisorId = context.assignedSupervisorId;
        const trainingType = context.trainingType;

        // 管理员可以评审所有培训
        if (userRole >= USER_ROLE.ADMIN) {
            return true;
        }

        // 指定的督导可以评审
        if (assignedSupervisorId && userId === assignedSupervisorId) {
            return true;
        }

        // 主任可以评审部门内的培训
        if (userRole === USER_ROLE.DIRECTOR && trainingType === 'department') {
            return true;
        }

        return false;
    }
}

export default FeaturePermissionManager;
