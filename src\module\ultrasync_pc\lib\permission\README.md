# 权限管理器系统

这是一个完整的权限管理器系统，用于管理Vue2多页面项目中的用户权限控制。

## 系统架构

```
PermissionManager (主权限管理器单例)
├── RoutePermissionManager (路由权限管理器)
├── ComponentPermissionManager (组件权限管理器)
├── FeaturePermissionManager (功能权限管理器)
└── RegionPermissionManager (区域权限管理器)
```

## 快速开始

### 1. 安装和初始化

```javascript
// 在main.js或入口文件中
import permissionManager from './lib/permission';

// 初始化权限管理器
const userInfo = {
    uid: '123',
    role: 2,
    type: 1,
    nickname: '用户名'
};

permissionManager.initialize(userInfo);
```

### 2. 作为Vue插件使用

```javascript
import Vue from 'vue';
import PermissionPlugin from './lib/permission';

Vue.use(PermissionPlugin, {
    autoInit: true,
    userInfo: {
        uid: '123',
        role: 2,
        type: 1
    }
});
```

## 使用方法

### 1. 路由权限控制

```javascript
// 在路由守卫中使用
router.beforeEach((to, from, next) => {
    if (permissionManager.checkRoutePermission(to.path)) {
        next();
    } else {
        const redirectRoute = permissionManager.getRedirectRoute(to.path);
        next(redirectRoute);
    }
});

// 在组件中使用
if (this.$checkRoute('/admin/users')) {
    // 有权限访问用户管理页面
}
```

### 2. 组件权限控制

```javascript
// 在Vue组件中
export default {
    computed: {
        // 检查菜单项是否可见
        isAdminMenuVisible() {
            return this.$checkComponent('menu', 'background_manage');
        },

        // 检查按钮是否禁用
        isDeleteButtonDisabled() {
            return !this.$checkComponent('button', 'delete');
        }
    }
}
```

### 3. 功能权限控制

```javascript
// 检查用户管理权限
if (permissionManager.checkFeaturePermission('user_management', 'create_user')) {
    // 可以创建用户
}

// 检查API权限
if (permissionManager.checkApiPermission('POST', '/api/user/create')) {
    // 可以调用创建用户API
}

// 检查数据权限
if (permissionManager.checkDataPermission('user_data', 'all_data')) {
    // 可以访问所有用户数据
}
```

### 4. 使用Vue指令

```vue
<template>
    <!-- 基础用法：没有权限时移除元素 -->
    <button v-permission="'user_create'">创建用户</button>

    <!-- 隐藏元素而不是移除 -->
    <button v-permission.hide="'user_delete'">删除用户</button>

    <!-- 禁用元素 -->
    <button v-permission.disable="'user_edit'">编辑用户</button>

    <!-- 指定权限类型 -->
    <div v-permission.route="'/admin'">管理员面板</div>
    <div v-permission.component="'menu.admin_panel'">管理菜单</div>
    <div v-permission.feature="'user_management'">用户管理</div>

    <!-- 复杂权限检查 -->
    <button v-permission="{
        type: 'feature',
        permission: 'user_management',
        action: 'create_user',
        context: { departmentId: 123 }
    }">创建部门用户</button>

    <!-- 使用regionPermissionKey和featurePermissionKey的组合权限检查 -->
    <button v-permission="{
        regionPermissionKey: 'live',
        featurePermissionKey: 'conference_management'
    }">开始会议</button>

    <!-- 只检查区域权限 -->
    <div v-permission="{
        regionPermissionKey: 'breastAI'
    }">乳腺AI功能</div>

    <!-- 只检查功能权限 -->
    <button v-permission="{
        featurePermissionKey: 'user_management'
    }">用户管理</button>
</template>
```

### 5. v-permission指令的高级用法

#### 5.1 组合权限检查

v-permission指令现在支持与checkPermission方法相同的regionPermissionKey和featurePermissionKey参数：

```vue
<template>
    <!-- 同时检查区域权限和功能权限 -->
    <button v-permission="{
        regionPermissionKey: 'live',
        featurePermissionKey: 'conference_management'
    }">
        开始会议
    </button>

    <!-- 只检查区域权限（等同于 v-permission.region="'breastAI'" ） -->
    <div v-permission="{
        regionPermissionKey: 'breastAI'
    }">
        乳腺AI功能面板
    </div>

    <!-- 只检查功能权限（等同于 v-permission.feature="'user_management'" ） -->
    <button v-permission="{
        featurePermissionKey: 'user_management'
    }">
        用户管理
    </button>

    <!-- 带上下文的组合权限检查 -->
    <button v-permission="{
        regionPermissionKey: 'tvwall',
        featurePermissionKey: 'advanced_display',
        context: { displayMode: 'multi' }
    }">
        多屏显示
    </button>
</template>
```

#### 5.2 权限检查逻辑

- **只传regionPermissionKey**：只校验区域配置
- **只传featurePermissionKey**：只校验功能权限
- **两个都传**：先校验区域配置，通过后再校验功能权限
- **都不传**：使用原有的type字段进行类型化权限检查

### 6. 批量权限检查

```javascript
// 批量检查多个权限
const permissions = permissionManager.batchCheckPermissions({
    routes: [
        { route: '/admin', key: 'admin_access' },
        { route: '/users', key: 'user_access' }
    ],
    components: [
        { component: 'menu', action: 'background_manage', key: 'admin_menu' },
        { component: 'button', action: 'delete', key: 'delete_btn' }
    ],
    features: [
        { feature: 'user_management', action: 'create_user', key: 'create_user' },
        { feature: 'group_management', action: 'edit_group', key: 'edit_group' }
    ]
});

console.log(permissions);
// {
//     admin_access: true,
//     user_access: true,
//     admin_menu: false,
//     delete_btn: false,
//     create_user: true,
//     edit_group: true
// }
```

### 6. 区域功能权限控制

区域权限管理器专门管理基于区域配置的功能权限（functionsStatus），这些权限在应用初始化时获取，不会因为退出登录而变化。

```javascript
// 检查区域功能权限
if (permissionManager.checkRegionPermission('live')) {
    // 当前区域支持直播功能
}

// 检查功能是否启用
if (permissionManager.isRegionFunctionEnabled('tvwall')) {
    // 电视墙功能已启用
}

// 检查功能在当前区域是否可用（包含CE区域检查）
if (permissionManager.isRegionFunctionAvailable('breastCases', { checkCE: true })) {
    // 乳腺病例库功能可用且不在CE区域限制中
}

// 获取所有启用的区域功能
const enabledFunctions = permissionManager.getEnabledRegionFunctions();
console.log('启用的功能:', enabledFunctions);

// 获取当前区域
const currentRegion = permissionManager.getCurrentRegion();
```

#### 在Vue组件中使用区域权限

```vue
<template>
    <!-- 使用指令控制区域功能显示 -->
    <button v-permission.region="'live'" @click="startLive">开始直播</button>
    <div v-permission.region="'tvwall'" class="tvwall-section">
        <h3>电视墙功能</h3>
    </div>

    <!-- 使用计算属性控制 -->
    <div v-if="canUseAI">
        <button v-if="canUseBreastAI" @click="openBreastAI">小麦同学</button>
        <button v-if="canUseDrAI" @click="openDrAI">DR助手</button>
    </div>
</template>

<script>
export default {
    computed: {
        canUseAI() {
            return this.$isRegionFunctionEnabled('ai');
        },

        canUseBreastAI() {
            return this.$isRegionFunctionAvailable('breastAI', { checkCE: true });
        },

        canUseDrAI() {
            return this.$isRegionFunctionEnabled('drAIAssistant');
        }
    },

    methods: {
        startLive() {
            if (!this.$checkRegionFunction('live')) {
                this.$message.error('当前区域不支持直播功能');
                return;
            }
            // 执行直播逻辑
        }
    }
}
</script>
```

#### 支持的区域功能

- `live`: 直播功能
- `library`: 图书馆功能
- `cloudStatistic`: 云端统计功能
- `breastCases`: 乳腺病例库功能
- `breastAI`: 小麦同学AI功能
- `drAIAssistant`: DR助手功能
- `groupset`: 群落功能
- `wechat`: 微信功能
- `obstetricalAI`: 产科AI功能
- `tvwall`: 电视墙功能
- `qcStatistics`: BI统计功能
- `referralCode`: 推荐码功能
- `ai`: AI应用功能
- `smartEdTechTraining`: 智能教培功能
- `ultrasoundQCReport`: 超声质控报告功能
- `club`: Mindray Club功能

## 角色权限配置

系统预定义了以下角色：

- `1`: 普通用户
- `2`: 管理员
- `3`: 高级管理员
- `4`: 仲裁者
- `5`: 超级管理员
- `6`: 采购者

## API参考

### 主要方法

- `initialize(userInfo, config)` - 初始化权限管理器
- `checkRoutePermission(routePath, context)` - 检查路由权限
- `checkComponentPermission(component, action, context)` - 检查组件权限
- `checkFeaturePermission(feature, action, context)` - 检查功能权限
- `checkRegionPermission(functionName, context)` - 检查区域功能权限
- `checkApiPermission(method, path, context)` - 检查API权限
- `checkDataPermission(dataType, scope, context)` - 检查数据权限
- `isComponentVisible(component, action, context)` - 获取组件可见性
- `isComponentDisabled(component, action, context)` - 获取组件禁用状态
- `isRegionFunctionEnabled(functionName)` - 检查区域功能是否启用
- `isRegionFunctionAvailable(functionName, options)` - 检查功能在当前区域是否可用
- `getEnabledRegionFunctions()` - 获取所有启用的区域功能
- `getCurrentRegion()` - 获取当前区域
- `updateUserInfo(userInfo)` - 更新用户信息
- `clearCache()` - 清除权限缓存
- `isAdmin()` - 检查是否为管理员
- `isSuperAdmin()` - 检查是否为超级管理员

### Vue混入方法

- `$checkRoute(routePath, context)` - 检查路由权限
- `$checkComponent(component, action, context)` - 检查组件权限
- `$checkFeature(feature, action, context)` - 检查功能权限
- `$checkRegionFunction(functionName, context)` - 检查区域功能权限
- `$isRegionFunctionEnabled(functionName)` - 检查区域功能是否启用
- `$isRegionFunctionAvailable(functionName, options)` - 检查功能在当前区域是否可用
- `$getCurrentRegion()` - 获取当前区域
- `$isAdmin()` - 检查是否为管理员
- `$isSuperAdmin()` - 检查是否为超级管理员
- `$getUserRole()` - 获取用户角色

## 扩展配置

### 添加自定义权限配置

```javascript
// 添加路由权限
permissionManager.getManager('route').addRoutePermission('/custom-route', {
    roles: [2, 3, 5],
    permissions: ['custom_permission']
});

// 添加组件权限
permissionManager.getManager('component').addComponentPermission('custom-component', {
    'custom_action': { roles: [2, 3], permissions: ['custom_action'] }
});

// 添加功能权限
permissionManager.getManager('feature').addFeaturePermission('custom-feature', {
    'custom_operation': { roles: [3, 5], permissions: ['custom_operation'] }
});
```

## 事件系统

权限管理器会触发以下事件：

- `permission:initialized` - 初始化完成
- `permission:userInfoUpdated` - 用户信息更新
- `permission:destroyed` - 管理器销毁

```javascript
// 监听事件
window.addEventListener('permission:initialized', (event) => {
    console.log('权限管理器初始化完成', event.detail);
});

// 在Vue组件中监听
this.$on('permission:userInfoUpdated', (userInfo) => {
    console.log('用户信息已更新', userInfo);
});
```

## 注意事项

1. 权限管理器必须在使用前调用 `initialize()` 方法进行初始化
2. 用户信息变更时需要调用 `updateUserInfo()` 方法更新权限
3. 权限配置支持角色和特定权限两种检查方式
4. 建议在路由守卫中集成路由权限检查
5. 组件权限检查建议在计算属性中使用，以便响应式更新
