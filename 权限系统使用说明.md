# 权限系统使用说明

## checkSpecificPermission 和 role 的区别

### 1. 角色权限（Role-based）
- **基于身份**：通过用户角色进行权限控制
- **粗粒度**：适用于大范围的功能模块访问控制
- **静态配置**：在权限配置中预定义角色要求

```javascript
// 角色权限示例
const featureConfig = {
    'backgroundManage': {
        'default': { 
            roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], // 只有管理员和超级管理员可以访问
            permissions: ['backgroundManage'] 
        },
    }
};
```

### 2. 特定权限（Specific Permission）
- **基于功能**：针对具体业务操作进行权限控制
- **细粒度**：可以精确控制到具体操作和资源
- **动态判断**：根据上下文、数据状态等进行动态权限判断
- **业务逻辑**：包含复杂的业务规则

```javascript
// 特定权限示例
checkSpecificPermission('user_edit', {
    targetUserId: 123,  // 要编辑的用户ID
    // 动态判断：用户只能编辑自己的信息，管理员可以编辑所有用户
});
```

## 权限检查流程

```javascript
// 权限检查的完整流程
checkPermissionConfig(config, context) {
    // 1. 首先检查角色权限
    if (config.roles && config.roles.length > 0) {
        const userRole = this.getUserRole();
        if (!config.roles.includes(userRole)) {
            return false; // 角色不匹配，直接拒绝
        }
    }

    // 2. 然后检查特定权限
    if (config.permissions && config.permissions.length > 0) {
        return config.permissions.every(permission =>
            this.checkSpecificPermission(permission, context)
        );
    }

    return true;
}
```

## 使用场景示例

### 1. 用户管理权限
```javascript
// 编辑用户信息
const canEditUser = permissionManager.checkSpecificPermission('user_edit', {
    targetUserId: targetUser.id
});
// 逻辑：管理员可以编辑所有用户，普通用户只能编辑自己

// 删除用户
const canDeleteUser = permissionManager.checkSpecificPermission('user_delete');
// 逻辑：只有超级管理员可以删除用户
```

### 2. 考试管理权限
```javascript
// 编辑考试
const canEditExam = permissionManager.checkSpecificPermission('exam_edit', {
    examCreatorId: exam.creatorId,
    examStatus: exam.status
});
// 逻辑：管理员可以编辑所有考试，创建者只能编辑未开始的考试

// 删除考试
const canDeleteExam = permissionManager.checkSpecificPermission('exam_delete', {
    examCreatorId: exam.creatorId,
    examStatus: exam.status,
    hasParticipants: exam.participantCount > 0
});
// 逻辑：有参与者的考试需要超级管理员权限才能删除
```

### 3. 文件操作权限
```javascript
// 删除文件
const canDeleteFile = permissionManager.checkSpecificPermission('file_delete', {
    fileOwnerId: file.ownerId,
    fileType: file.type
});
// 逻辑：用户可以删除自己的文件，但系统配置文件需要管理员权限
```

### 4. 数据访问权限
```javascript
// 访问部门数据
const canAccessDeptData = permissionManager.checkSpecificPermission('department_data_access', {
    userDepartmentId: currentUser.departmentId,
    targetDepartmentId: targetDepartment.id
});
// 逻辑：用户只能访问自己部门的数据，管理员可以访问所有部门
```

## 权限配置示例

### 完整的权限配置
```javascript
const permissionConfig = {
    roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN], // 角色要求
    permissions: ['user_edit', 'data_export'] // 特定权限要求
};

// 权限检查逻辑：
// 1. 用户角色必须是管理员或超级管理员
// 2. 同时必须具有 user_edit 和 data_export 权限
// 3. 两个条件都满足才能通过权限检查
```

## 扩展权限

### 添加新的特定权限
```javascript
// 在 checkSpecificPermission 的 permissionMap 中添加
'new_permission': () => {
    // 自定义权限检查逻辑
    const userRole = this.getUserRole();
    const userId = this.getUserId();
    
    // 根据业务需求实现权限逻辑
    return userRole >= USER_ROLE.ADMIN && someBusinessLogic();
},
```

## 最佳实践

1. **角色权限用于模块级控制**：如整个后台管理功能的访问
2. **特定权限用于操作级控制**：如编辑、删除等具体操作
3. **结合上下文信息**：传入相关的业务数据进行动态判断
4. **安全优先**：未定义的权限默认拒绝访问
5. **错误处理**：权限检查异常时默认拒绝访问
