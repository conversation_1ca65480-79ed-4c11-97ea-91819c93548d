/**
 * v-permission指令使用示例
 * 展示如何使用regionPermissionKey和featurePermissionKey进行权限控制
 */

// Vue组件示例
export const VPermissionExamples = {
    template: `
        <div class="permission-examples">
            <h2>v-permission指令示例</h2>
            
            <!-- 1. 基础用法 -->
            <section class="basic-usage">
                <h3>1. 基础用法</h3>
                
                <!-- 字符串模式：默认使用checkPermission -->
                <button v-permission="'user_create'">创建用户</button>
                
                <!-- 修饰符模式 -->
                <button v-permission.region="'live'">开始直播</button>
                <button v-permission.feature="'user_management'">用户管理</button>
                <div v-permission.route="'/admin'">管理员面板</div>
            </section>

            <!-- 2. 组合权限检查 -->
            <section class="combined-permissions">
                <h3>2. 组合权限检查（新功能）</h3>
                
                <!-- 同时检查区域权限和功能权限 -->
                <button v-permission="{
                    regionPermissionKey: 'live',
                    featurePermissionKey: 'conference_management'
                }">
                    开始会议（需要直播功能 + 会议管理权限）
                </button>

                <!-- 只检查区域权限 -->
                <div v-permission="{
                    regionPermissionKey: 'breastAI'
                }" class="ai-panel">
                    <h4>乳腺AI功能</h4>
                    <p>此面板只在区域支持乳腺AI时显示</p>
                </div>

                <!-- 只检查功能权限 -->
                <button v-permission="{
                    featurePermissionKey: 'user_management'
                }">
                    用户管理（只检查功能权限）
                </button>

                <!-- 带上下文的组合权限检查 -->
                <button v-permission="{
                    regionPermissionKey: 'tvwall',
                    featurePermissionKey: 'advanced_display',
                    context: { displayMode: 'multi' }
                }">
                    多屏显示
                </button>
            </section>

            <!-- 3. 不同的处理方式 -->
            <section class="different-behaviors">
                <h3>3. 不同的处理方式</h3>
                
                <!-- 默认：移除元素 -->
                <button v-permission="{
                    regionPermissionKey: 'qcStatistics'
                }">
                    QC统计（无权限时移除）
                </button>

                <!-- 隐藏元素 -->
                <button v-permission.hide="{
                    regionPermissionKey: 'drAIAssistant'
                }">
                    DR助手（无权限时隐藏）
                </button>

                <!-- 禁用元素 -->
                <button v-permission.disable="{
                    regionPermissionKey: 'webShareScreen',
                    featurePermissionKey: 'screen_sharing'
                }">
                    屏幕共享（无权限时禁用）
                </button>
            </section>

            <!-- 4. 实际业务场景示例 -->
            <section class="business-scenarios">
                <h3>4. 实际业务场景示例</h3>
                
                <!-- 聊天工具栏权限控制 -->
                <div class="chat-toolbar">
                    <!-- 直播按钮：需要区域支持直播功能 -->
                    <button v-permission="{
                        regionPermissionKey: 'live'
                    }" @click="startLive">
                        <i class="icon-live"></i>
                        开始直播
                    </button>

                    <!-- 产科AI：需要区域支持 + 用户有AI使用权限 -->
                    <button v-permission="{
                        regionPermissionKey: 'obstetricalAI',
                        featurePermissionKey: 'ai_assistant'
                    }" @click="openObstetricalAI">
                        <i class="icon-ai"></i>
                        产科AI
                    </button>

                    <!-- DR分析：需要区域支持 + 设备连接 + 用户权限 -->
                    <button v-permission="{
                        regionPermissionKey: 'drAIAssistant',
                        featurePermissionKey: 'dr_analysis',
                        context: { requireDeviceConnection: true }
                    }" @click="openDrAnalysis">
                        <i class="icon-dr"></i>
                        DR分析
                    </button>

                    <!-- 预约会议：群聊 + 直播功能 + 会议管理权限 -->
                    <button v-permission="{
                        regionPermissionKey: 'live',
                        featurePermissionKey: 'conference_reservation'
                    }" v-if="isGroupChat" @click="reserveConference">
                        <i class="icon-calendar"></i>
                        预约会议
                    </button>
                </div>

                <!-- 管理面板权限控制 -->
                <div class="admin-panel" v-permission="{
                    featurePermissionKey: 'admin_access'
                }">
                    <h4>管理面板</h4>
                    
                    <!-- 用户管理 -->
                    <div v-permission="{
                        featurePermissionKey: 'user_management'
                    }">
                        <button @click="manageUsers">用户管理</button>
                    </div>

                    <!-- 系统配置：需要超级管理员权限 -->
                    <div v-permission="{
                        featurePermissionKey: 'system_config'
                    }">
                        <button @click="systemConfig">系统配置</button>
                    </div>

                    <!-- 区域功能配置：需要区域管理权限 -->
                    <div v-permission="{
                        featurePermissionKey: 'region_management'
                    }">
                        <button @click="regionConfig">区域功能配置</button>
                    </div>
                </div>
            </section>
        </div>
    `,

    data() {
        return {
            isGroupChat: true
        };
    },

    methods: {
        startLive() {
            console.log('开始直播');
        },
        
        openObstetricalAI() {
            console.log('打开产科AI');
        },
        
        openDrAnalysis() {
            console.log('打开DR分析');
        },
        
        reserveConference() {
            console.log('预约会议');
        },
        
        manageUsers() {
            console.log('用户管理');
        },
        
        systemConfig() {
            console.log('系统配置');
        },
        
        regionConfig() {
            console.log('区域功能配置');
        }
    }
};

// 使用说明
export const usageNotes = {
    // 权限检查逻辑说明
    permissionLogic: {
        // 只传regionPermissionKey：只校验区域配置
        regionOnly: {
            regionPermissionKey: 'live'
            // 等同于：permissionManager.checkRegionPermission('live')
        },

        // 只传featurePermissionKey：只校验功能权限
        featureOnly: {
            featurePermissionKey: 'user_management'
            // 等同于：permissionManager.checkFeaturePermission('user_management')
        },

        // 两个都传：先校验区域配置，通过后再校验功能权限
        combined: {
            regionPermissionKey: 'live',
            featurePermissionKey: 'conference_management'
            // 等同于：permissionManager.checkPermission({
            //     regionPermissionKey: 'live',
            //     featurePermissionKey: 'conference_management'
            // })
        }
    },

    // 修饰符说明
    modifiers: {
        // 默认：移除元素
        default: 'v-permission="permission"',
        
        // 隐藏元素（display: none）
        hide: 'v-permission.hide="permission"',
        
        // 禁用元素（disabled + class）
        disable: 'v-permission.disable="permission"'
    },

    // 兼容性说明
    compatibility: {
        // 原有的修饰符方式仍然支持
        oldWay: [
            'v-permission.region="\'live\'"',
            'v-permission.feature="\'user_management\'"',
            'v-permission.route="\'/admin\'"'
        ],

        // 新的对象方式
        newWay: [
            'v-permission="{ regionPermissionKey: \'live\' }"',
            'v-permission="{ featurePermissionKey: \'user_management\' }"',
            'v-permission="{ type: \'route\', permission: \'/admin\' }"'
        ]
    }
};
