# 权限实时更新系统使用说明

## 概述

权限实时更新系统提供了优雅的方式来处理用户角色变化时的页面更新，确保所有权限相关的UI元素能够实时响应权限变化。

## 核心特性

### 1. 自动响应式更新
- **计算属性自动更新**：依赖 `permissionVersion` 的计算属性会在权限变化时自动重新计算
- **组件状态同步**：`currentUserRole` 和 `currentUserId` 会自动同步最新的用户信息
- **指令实时更新**：`v-permission` 指令会自动响应权限变化

### 2. 事件驱动机制
- **权限变化事件**：`permission:changed` 事件在权限发生变化时触发
- **组件钩子**：组件可以实现 `onPermissionChanged` 方法来处理权限变化
- **全局事件总线**：支持Vue事件总线和浏览器原生事件

### 3. 优雅的更新策略
- **防抖处理**：避免频繁更新造成的性能问题
- **批量更新**：支持批量更新多个组件
- **渐进式更新**：优先使用响应式更新，必要时使用强制更新

## 使用方法

### 1. 基本权限检查（自动响应式）

```vue
<template>
  <div>
    <!-- 使用计算属性，自动响应权限变化 -->
    <el-button v-if="canManageUsers" @click="openUserManage">
      用户管理
    </el-button>
    
    <!-- 使用v-permission指令，自动响应权限变化 -->
    <el-button v-permission="'backgroundManage'" @click="openBackgroundManage">
      后台管理
    </el-button>
    
    <!-- 隐藏模式 -->
    <el-button v-permission.hide="'admin'" @click="adminAction">
      管理员功能
    </el-button>
    
    <!-- 禁用模式 -->
    <el-button v-permission.disable="'user_edit'" @click="editUser">
      编辑用户
    </el-button>
  </div>
</template>

<script>
export default {
  computed: {
    // 依赖permissionVersion，权限变化时自动重新计算
    canManageUsers() {
      this.permissionVersion; // 触发响应式依赖
      return this.$checkPermission('user_management');
    }
  },
  
  // 权限变化时的回调（可选）
  onPermissionChanged(changeInfo) {
    console.log('权限发生变化:', changeInfo);
    
    if (changeInfo.type === 'userInfo') {
      // 处理用户信息变化
      this.handleUserInfoChange(changeInfo);
    }
  }
}
</script>
```

### 2. 手动更新用户权限

```javascript
import permissionUpdater from '@/lib/permission/permissionUpdater.js';

// 基本更新
async function updateUserRole(newUserInfo) {
  const result = await permissionUpdater.updateUserPermissions(newUserInfo);
  
  if (result.success) {
    console.log('权限更新成功:', result);
  } else {
    console.error('权限更新失败:', result.error);
  }
}

// 自定义更新选项
async function updateUserRoleWithOptions(newUserInfo) {
  const result = await permissionUpdater.updateUserPermissions(newUserInfo, {
    showMessage: true,
    messageType: 'success',
    customMessage: '权限已成功更新！',
    forceUpdate: true
  });
}

// 检查是否需要更新
function checkAndUpdate(newUserInfo) {
  if (permissionUpdater.needsUpdate(newUserInfo)) {
    permissionUpdater.updateUserPermissions(newUserInfo);
  }
}
```

### 3. 组件中处理权限变化

```vue
<script>
export default {
  data() {
    return {
      dialogVisible: false
    };
  },
  
  computed: {
    // 响应式权限检查
    filteredMenuItems() {
      this.permissionVersion; // 确保权限变化时重新计算
      
      return this.menuItems.filter(item => {
        if (item.requiresAdmin) {
          return this.$isAdmin();
        }
        if (item.permission) {
          return this.$checkPermission(item.permission);
        }
        return true;
      });
    }
  },
  
  watch: {
    // 监听角色变化
    currentUserRole(newRole, oldRole) {
      console.log(`用户角色从 ${oldRole} 变更为 ${newRole}`);
      
      // 如果失去管理员权限，关闭管理对话框
      if (this.dialogVisible && !this.$isAdmin()) {
        this.dialogVisible = false;
        this.$message.warning('权限已变更，对话框已关闭');
      }
    }
  },
  
  // 权限变化回调
  onPermissionChanged(changeInfo) {
    if (changeInfo.type === 'userInfo' && changeInfo.hasRoleChanged) {
      // 角色变化时的特殊处理
      this.handleRoleChange(changeInfo.oldRole, changeInfo.newRole);
    }
  },
  
  methods: {
    handleRoleChange(oldRole, newRole) {
      // 实现角色变化时的业务逻辑
      if (oldRole >= 2 && newRole < 2) {
        // 从管理员降级为普通用户
        this.$message.warning('您的管理员权限已被撤销');
        this.$router.push('/dashboard'); // 跳转到安全页面
      } else if (oldRole < 2 && newRole >= 2) {
        // 从普通用户升级为管理员
        this.$message.success('您已获得管理员权限');
      }
    }
  }
}
</script>
```

### 4. 在API调用中更新权限

```javascript
// 登录后更新权限
async function login(credentials) {
  try {
    const response = await api.login(credentials);
    const userInfo = response.data.user;
    
    // 更新权限信息
    await permissionUpdater.updateUserPermissions(userInfo, {
      showMessage: true,
      messageType: 'success',
      customMessage: '登录成功！'
    });
    
    return response;
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
}

// 角色变更后更新权限
async function changeUserRole(userId, newRole) {
  try {
    await api.changeUserRole(userId, newRole);
    
    // 如果是当前用户，更新权限
    if (userId === getCurrentUserId()) {
      const updatedUserInfo = { ...getCurrentUserInfo(), role: newRole };
      await permissionUpdater.updateUserPermissions(updatedUserInfo);
    }
  } catch (error) {
    console.error('角色变更失败:', error);
    throw error;
  }
}
```

## 最佳实践

### 1. 使用响应式计算属性
```javascript
computed: {
  // ✅ 推荐：依赖permissionVersion的响应式计算属性
  canEdit() {
    this.permissionVersion;
    return this.$checkPermission('edit');
  }
}

// ❌ 不推荐：直接在模板中调用方法
// <button v-if="$checkPermission('edit')">编辑</button>
```

### 2. 合理使用v-permission指令
```html
<!-- ✅ 推荐：使用hide模式，便于恢复 -->
<button v-permission.hide="'admin'">管理员功能</button>

<!-- ✅ 推荐：使用disable模式，保持UI一致性 -->
<button v-permission.disable="'edit'">编辑</button>

<!-- ⚠️ 谨慎使用：移除模式，恢复较复杂 -->
<button v-permission="'delete'">删除</button>
```

### 3. 实现权限变化回调
```javascript
// ✅ 推荐：实现onPermissionChanged方法
onPermissionChanged(changeInfo) {
  // 处理权限变化的业务逻辑
  this.handlePermissionChange(changeInfo);
}
```

### 4. 错误处理
```javascript
async function updatePermissions(userInfo) {
  try {
    const result = await permissionUpdater.updateUserPermissions(userInfo);
    if (!result.success) {
      // 处理更新失败
      console.error('权限更新失败:', result.error);
      // 可能需要刷新页面或重新登录
    }
  } catch (error) {
    console.error('权限更新异常:', error);
    // 异常处理逻辑
  }
}
```

## 注意事项

1. **性能考虑**：避免在计算属性中进行复杂的权限检查，使用缓存机制
2. **安全性**：前端权限检查仅用于UI控制，后端必须进行真正的权限验证
3. **兼容性**：确保在权限管理器未初始化时有合适的降级处理
4. **调试**：开启控制台日志，便于调试权限更新过程

## 事件列表

- `permission:initialized` - 权限管理器初始化完成
- `permission:regionInitialized` - 区域权限初始化完成
- `permission:changed` - 权限发生变化
- `permission:forceUpdate` - 强制更新事件
