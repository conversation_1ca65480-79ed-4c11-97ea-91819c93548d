# 选择性权限混入使用说明

## 概述

新的权限系统支持选择性混入，只有明确标识需要权限响应式更新的组件才会自动应用权限混入，避免了全局混入可能带来的性能问题。

## 权限标识方式

### 1. 简单启用方式
```javascript
export default {
  name: 'MyComponent',
  permission: true, // 启用权限响应式更新
  // ...
}
```

### 2. 对象配置方式
```javascript
export default {
  name: 'MyComponent',
  permission: {
    reactive: true // 启用权限响应式更新
  },
  // ...
}
```

### 3. 组件名称白名单方式
```javascript
// 在Vue插件安装时配置
Vue.use(PermissionPlugin, {
  components: ['LeftSidebar', 'UserManagement', 'AdminPanel']
});
```

### 4. 自动检测方式
系统会自动检测组件是否包含权限相关的计算属性：
- 包含 `permission`、`Permission` 关键字的计算属性
- 包含 `canAccess`、`hasAccess` 关键字的计算属性
- 包含 `filtered` 和 `menu` 关键字的计算属性（如 `filteredMenuItems`）

## 使用示例

### 启用权限响应式的组件

```vue
<template>
  <div>
    <!-- 响应式权限检查 -->
    <el-button v-if="canManageUsers" @click="openUserManage">
      用户管理
    </el-button>
    
    <!-- v-permission 指令 -->
    <el-button v-permission.hide="'backgroundManage'">
      后台管理
    </el-button>
  </div>
</template>

<script>
export default {
  name: 'AdminPanel',
  permission: true, // 启用权限响应式
  
  computed: {
    // 自动响应权限变化
    canManageUsers() {
      this.permissionVersion; // 触发响应式依赖
      return this.$checkPermission('user_management');
    }
  },
  
  // 权限变化回调
  onPermissionChanged(changeInfo) {
    console.log('权限发生变化:', changeInfo);
  }
}
</script>
```

### 普通组件（不启用权限响应式）

```vue
<template>
  <div>
    <!-- 手动权限检查，不会自动响应变化 -->
    <el-button v-if="canAccess" @click="doSomething">
      操作按钮
    </el-button>
    
    <!-- v-permission 指令仍然有效 -->
    <el-button v-permission.hide="'admin'">
      管理员功能
    </el-button>
  </div>
</template>

<script>
export default {
  name: 'NormalComponent',
  // 没有 permission 标识
  
  computed: {
    canAccess() {
      // 手动检查，不会自动响应权限变化
      return this.$permission.checkPermission('some_permission');
    }
  }
}
</script>
```

## 可用的响应式数据

启用权限响应式的组件会自动获得以下响应式数据：

```javascript
data() {
  return {
    // 权限管理器初始化状态
    permissionInitialized: false,
    regionPermissionInitialized: false,
    
    // 权限版本号，用于强制更新计算属性
    permissionVersion: 0,
    
    // 当前用户信息
    currentUserRole: 0,
    currentUserId: null
  };
}
```

## 可用的权限方法

所有组件（无论是否启用权限响应式）都可以使用以下方法：

```javascript
methods: {
  // 通用权限检查
  this.$checkPermission('permission_name'),
  
  // 路由权限检查
  this.$checkRoute('/admin/users'),
  
  // 组件权限检查
  this.$checkComponent('UserList', 'edit'),
  
  // 功能权限检查
  this.$checkFeature('user_management', 'create'),
  
  // 角色检查
  this.$isAdmin(),
  this.$isSuperAdmin(),
  this.$getUserRole(),
  
  // 区域权限检查
  this.$checkRegionFunction('function_name'),
  this.$isRegionFunctionEnabled('function_name')
}
```

## 权限变化回调

启用权限响应式的组件可以实现 `onPermissionChanged` 方法：

```javascript
export default {
  permission: true,
  
  onPermissionChanged(changeInfo) {
    console.log('权限变化信息:', changeInfo);
    
    // changeInfo 包含以下信息：
    // - type: 'userInfo'
    // - oldRole: 旧角色
    // - newRole: 新角色
    // - oldUserId: 旧用户ID
    // - newUserId: 新用户ID
    // - hasRoleChanged: 角色是否变化
    // - hasUserChanged: 用户是否变化
    
    if (changeInfo.hasRoleChanged) {
      // 处理角色变化
      this.handleRoleChange(changeInfo.oldRole, changeInfo.newRole);
    }
  }
}
```

## v-permission 指令

v-permission 指令对所有组件都有效，无论是否启用权限响应式：

```html
<!-- 移除元素（默认） -->
<button v-permission="'admin'">管理员功能</button>

<!-- 隐藏元素 -->
<button v-permission.hide="'admin'">管理员功能</button>

<!-- 禁用元素 -->
<button v-permission.disable="'edit'">编辑功能</button>

<!-- 复杂权限检查 -->
<button v-permission="{
  type: 'feature',
  permission: 'user_management',
  action: 'create',
  context: { departmentId: 123 }
}">创建用户</button>
```

## 性能优化

### 1. 选择性启用
只在需要权限响应式更新的组件中启用权限标识：

```javascript
// ✅ 推荐：只在需要的组件中启用
export default {
  name: 'AdminPanel',
  permission: true, // 这个组件需要响应权限变化
}

// ✅ 推荐：普通组件不启用
export default {
  name: 'StaticComponent',
  // 不添加 permission 标识
}
```

### 2. 合理使用计算属性
```javascript
computed: {
  // ✅ 推荐：依赖 permissionVersion
  canEdit() {
    this.permissionVersion;
    return this.$checkPermission('edit');
  },
  
  // ❌ 避免：直接在模板中调用方法
  // <button v-if="$checkPermission('edit')">编辑</button>
}
```

### 3. 批量权限检查
```javascript
computed: {
  permissions() {
    this.permissionVersion;
    return {
      canEdit: this.$checkPermission('edit'),
      canDelete: this.$checkPermission('delete'),
      canCreate: this.$checkPermission('create')
    };
  }
}
```

## 调试和监控

### 1. 控制台日志
启用权限响应式的组件会在控制台输出相关日志：

```
[Permission] Applying permission mixin to component: LeftSidebar
[Permission] Handling permission change in component: LeftSidebar
```

### 2. 检查组件状态
```javascript
// 检查组件是否启用了权限响应式
console.log(this.permissionVersion !== undefined);

// 检查当前权限状态
console.log({
  role: this.currentUserRole,
  userId: this.currentUserId,
  initialized: this.permissionInitialized
});
```

## 迁移指南

### 从全局混入迁移
1. 在需要权限响应式的组件中添加 `permission: true`
2. 确保计算属性中包含 `this.permissionVersion;`
3. 测试权限变化是否正常响应

### 新组件开发
1. 如果组件需要响应权限变化，添加 `permission: true`
2. 使用响应式计算属性进行权限检查
3. 可选：实现 `onPermissionChanged` 回调处理权限变化
