<template>
  <div class="normal-test">
    <h3>普通组件 (无权限标识)</h3>
    
    <div class="test-section">
      <p>这个组件没有权限标识，不会自动应用权限混入</p>
      <p>当前用户角色: {{ userRole }}</p>
      <p>权限初始化状态: {{ permissionInitialized }}</p>
    </div>
    
    <div class="test-section">
      <h4>手动权限检查</h4>
      <el-button v-if="canAccess" type="primary">
        后台管理 (手动检查)
      </el-button>
      <el-button v-else disabled>
        后台管理 (无权限)
      </el-button>
    </div>
    
    <div class="test-section">
      <h4>v-permission 指令仍然有效</h4>
      <el-button v-permission.hide="'backgroundManage'" type="success">
        后台管理 (指令)
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NormalTestComponent',
  // 注意：这里没有 permission 标识
  
  data() {
    return {
      permissionInitialized: false
    };
  },
  
  computed: {
    userRole() {
      // 手动获取用户角色，不会自动响应权限变化
      return this.$permission.getUserRole();
    },
    
    canAccess() {
      // 手动权限检查，不会自动响应权限变化
      return this.$permission.checkPermission('backgroundManage');
    }
  },
  
  created() {
    // 手动检查权限初始化状态
    this.permissionInitialized = this.$permission.isInitialized();
    
    // 手动监听权限变化（如果需要的话）
    const handlePermissionChange = () => {
      console.log('[NormalTest] 手动检测到权限变化');
      this.$forceUpdate(); // 手动强制更新
    };
    
    window.addEventListener('permission:changed', handlePermissionChange);
    
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('permission:changed', handlePermissionChange);
    });
  }
}
</script>

<style scoped>
.normal-test {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin: 10px;
  background-color: #fafafa;
}

.test-section {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.test-section h4 {
  margin-top: 0;
  color: #666;
}
</style>
