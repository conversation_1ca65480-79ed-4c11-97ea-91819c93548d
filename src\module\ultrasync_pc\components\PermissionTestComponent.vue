<template>
  <div class="permission-test">
    <h3>权限测试组件</h3>
    
    <div class="test-section">
      <h4>1. 使用 permission: true 标识</h4>
      <p>当前用户角色: {{ currentUserRole }}</p>
      <p>权限版本: {{ permissionVersion }}</p>
      <p>是否管理员: {{ isAdminUser }}</p>
    </div>
    
    <div class="test-section">
      <h4>2. 响应式权限检查</h4>
      <el-button v-if="canAccessBackground" type="primary">
        后台管理 (响应式)
      </el-button>
      <el-button v-else disabled>
        后台管理 (无权限)
      </el-button>
    </div>
    
    <div class="test-section">
      <h4>3. v-permission 指令测试</h4>
      <el-button v-permission.hide="'backgroundManage'" type="success">
        后台管理 (指令隐藏)
      </el-button>
      <el-button v-permission.disable="'backgroundManage'" type="warning">
        后台管理 (指令禁用)
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PermissionTestComponent',
  // 方式1: 简单启用权限响应式
  permission: true,
  
  computed: {
    // 响应式权限检查
    canAccessBackground() {
      this.permissionVersion; // 触发响应式依赖
      return this.$checkPermission('backgroundManage');
    },
    
    isAdminUser() {
      this.permissionVersion; // 触发响应式依赖
      return this.$isAdmin();
    }
  },
  
  // 权限变化回调
  onPermissionChanged(changeInfo) {
    console.log('[PermissionTest] 权限发生变化:', changeInfo);
    this.$message.info(`测试组件检测到权限变化: ${changeInfo.oldRole} -> ${changeInfo.newRole}`);
  }
}
</script>

<style scoped>
.permission-test {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin: 10px;
}

.test-section {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.test-section h4 {
  margin-top: 0;
  color: #333;
}
</style>
