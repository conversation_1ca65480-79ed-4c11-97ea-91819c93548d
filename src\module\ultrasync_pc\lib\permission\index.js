/**
 * 权限管理器入口文件
 * 提供统一的权限管理接口
 */

import permissionManager, { PermissionManager } from './PermissionManager.js';
import BasePermissionManager from './BasePermissionManager.js';
import RoutePermissionManager from './RoutePermissionManager.js';
import ComponentPermissionManager from './ComponentPermissionManager.js';
import FeaturePermissionManager from './FeaturePermissionManager.js';

// 导出主要的权限管理器实例
export default permissionManager;

// 导出所有类，供需要自定义的场景使用
export {
    PermissionManager,
    BasePermissionManager,
    RoutePermissionManager,
    ComponentPermissionManager,
    FeaturePermissionManager
};

// 便捷方法导出
export const {
    checkPermission,
    checkRoutePermission,
    checkComponentPermission,
    checkFeaturePermission,
    checkRegionPermission,
    checkApiPermission,
    checkDataPermission,
    checkElementPermission,
    isComponentVisible,
    isComponentDisabled,
    getAccessibleRoutes,
    getAvailableActions,
    getRedirectRoute,
    batchCheckPermissions,
    updateUserInfo,
    clearCache,
    getUserInfo,
    getUserRole,
    getUserId,
    isAdmin,
    isSuperAdmin,
    isInitialized,
    initialize,
    initializeRegionPermissions,
    destroy,
    isRegionFunctionEnabled,
    getEnabledRegionFunctions,
    getCurrentRegion,
    isRegionFunctionAvailable,
    getRegionMappedPermissions,
    checkAllRegionMappedPermissions,
    getAllEnabledMappedPermissions,
    getRegionPermissionMappingSummary
} = permissionManager;

/**
 * Vue插件安装函数
 * @param {Object} Vue - Vue构造函数
 * @param {Object} options - 插件选项
 */
export function install(Vue, options = {}) {
    // 将权限管理器添加到Vue原型
    Vue.prototype.$permission = permissionManager;


    // 创建权限混入对象
    const permissionMixin = {
        data() {
            return {
                // 响应式的权限管理器初始化状态
                permissionInitialized: permissionManager.isInitialized(),
                regionPermissionInitialized: permissionManager.regionInitialized,
                // 权限版本号，用于强制更新权限相关的计算属性
                permissionVersion: 0,
                // 用户角色信息，用于监听角色变化
                currentUserRole: permissionManager.getUserRole(),
                currentUserId: permissionManager.getUserId()
            };
        },

        created() {
            // 监听权限管理器初始化事件
            const updateInitStatus = () => {
                this.permissionInitialized = permissionManager.isInitialized();
                this.regionPermissionInitialized = permissionManager.regionInitialized;
            };

            // 监听权限变化事件
            const handlePermissionChange = (event) => {
                const changeInfo = event.detail || event;
                console.log('[Permission] Handling permission change in component:', this.$options.name, changeInfo);

                // 更新用户信息
                this.currentUserRole = permissionManager.getUserRole();
                this.currentUserId = permissionManager.getUserId();

                // 增加版本号，强制更新所有权限相关的计算属性
                this.permissionVersion++;

                // 触发组件的权限变化钩子（如果存在）
                if (typeof this.onPermissionChanged === 'function') {
                    this.onPermissionChanged(changeInfo);
                }

                // 强制重新渲染（作为最后手段）
                // this.$nextTick(() => {
                //     this.$forceUpdate();
                // });
            };

            // 监听初始化完成事件
            window.addEventListener('permission:initialized', updateInitStatus);
            window.addEventListener('permission:regionInitialized', updateInitStatus);
            window.addEventListener('permission:changed', handlePermissionChange);

            // 同时监听 Vue 实例事件总线
            if (window.vm && window.vm.$on) {
                window.vm.$on('permission:initialized', updateInitStatus);
                window.vm.$on('permission:changed', handlePermissionChange);
            }
            if (window.vm && window.vm.$root && window.vm.$root.eventBus && window.vm.$root.eventBus.$on) {
                window.vm.$root.eventBus.$on('permission:regionInitialized', updateInitStatus);
                window.vm.$root.eventBus.$on('permission:changed', handlePermissionChange);
            }

            // 组件销毁时移除监听器
            this.$once('hook:beforeDestroy', () => {
                window.removeEventListener('permission:initialized', updateInitStatus);
                window.removeEventListener('permission:regionInitialized', updateInitStatus);
                window.removeEventListener('permission:changed', handlePermissionChange);

                if (window.vm && window.vm.$off) {
                    window.vm.$off('permission:initialized', updateInitStatus);
                    window.vm.$off('permission:changed', handlePermissionChange);
                }
                if (window.vm && window.vm.$root && window.vm.$root.eventBus && window.vm.$root.eventBus.$off) {
                    window.vm.$root.eventBus.$off('permission:regionInitialized', updateInitStatus);
                    window.vm.$root.eventBus.$off('permission:changed', handlePermissionChange);
                }
            });
        },

        methods: {
            // 通用权限检查方法
            $checkPermission(permission, context = {}) {
                return permissionManager.checkPermission(permission, context);
            },

            // 检查路由权限
            $checkRoute(routePath, context = {}) {
                return permissionManager.checkRoutePermission(routePath, context);
            },

            // 检查组件权限
            $checkComponent(component, action = null, context = {}) {
                return permissionManager.checkComponentPermission(component, action, context);
            },

            // 检查功能权限
            $checkFeature(feature, action = null, context = {}) {
                return permissionManager.checkFeaturePermission(feature, action, context);
            },

            // 检查区域功能权限
            $checkRegionFunction(functionName, context = {}) {
                return permissionManager.checkRegionPermission(functionName, context);
            },

            // 检查区域功能是否启用
            $isRegionFunctionEnabled(functionName) {
                return permissionManager.isRegionFunctionEnabled(functionName);
            },

            // 检查功能在当前区域是否可用
            $isRegionFunctionAvailable(functionName, options = {}) {
                return permissionManager.isRegionFunctionAvailable(functionName, options);
            },

            // 获取当前区域
            $getCurrentRegion() {
                return permissionManager.getCurrentRegion();
            },

            // 获取指定区域功能的所有映射权限
            $getRegionMappedPermissions(regionFunction) {
                return permissionManager.getRegionMappedPermissions(regionFunction);
            },

            // 检查指定区域功能的所有映射权限
            $checkAllRegionMappedPermissions(regionFunction) {
                return permissionManager.checkAllRegionMappedPermissions(regionFunction);
            },

            // 获取所有启用的映射权限
            $getAllEnabledMappedPermissions() {
                return permissionManager.getAllEnabledMappedPermissions();
            },

            // 获取权限映射关系摘要
            $getRegionPermissionMappingSummary() {
                return permissionManager.getRegionPermissionMappingSummary();
            },

            // 检查是否为管理员
            $isAdmin() {
                return permissionManager.isAdmin();
            },

            // 检查是否为超级管理员
            $isSuperAdmin() {
                return permissionManager.isSuperAdmin();
            },

            // 获取用户角色
            $getUserRole() {
                return permissionManager.getUserRole();
            },
        }
    };

    // 检查组件是否需要权限混入
    const shouldApplyPermissionMixin = (componentOptions) => {
        // 检查组件选项中的权限标识
        if (componentOptions.permission === true) {
            return true;
        }

        if (componentOptions.permission && typeof componentOptions.permission === 'object') {
            return componentOptions.permission.reactive === true;
        }

        // 检查组件名称是否在白名单中
        const permissionComponents = options.components || [];
        if (permissionComponents.includes(componentOptions.name)) {
            return true;
        }

        // 检查是否有权限相关的计算属性或方法
        if (componentOptions.computed) {
            const computedKeys = Object.keys(componentOptions.computed);
            const hasPermissionComputed = computedKeys.some(key =>
                key.includes('permission') ||
                key.includes('Permission') ||
                key.includes('canAccess') ||
                key.includes('hasAccess') ||
                key.includes('filtered') && key.includes('menu')
            );
            if (hasPermissionComputed) {
                return true;
            }
        }

        return false;
    };

    // 应用选择性混入
    Vue.mixin({
        beforeCreate() {
            // 检查当前组件是否需要权限混入
            if (shouldApplyPermissionMixin(this.$options)) {
                console.log('[Permission] Applying permission mixin to component:', this.$options.name);

                // 动态应用权限混入
                const mixins = this.$options.mixins || [];
                mixins.push(permissionMixin);
                this.$options.mixins = mixins;

                // 手动调用权限混入的data函数
                if (permissionMixin.data) {
                    const permissionData = permissionMixin.data.call(this);
                    Object.assign(this.$data, permissionData);
                }
            }
        }
    });

    // 添加全局指令 v-permission
    Vue.directive('permission', {
        bind(el, binding) {
            // 保存原始状态
            el._originalDisplay = el.style.display;
            el._originalDisabled = el.disabled;
            el._originalClass = el.className;
            el._permissionBinding = binding;

            // 执行权限检查
            this.checkPermission(el, binding);

            // 监听权限变化
            const handlePermissionChange = () => {
                this.checkPermission(el, binding);
            };

            el._permissionChangeHandler = handlePermissionChange;
            window.addEventListener('permission:changed', handlePermissionChange);
        },

        update(el, binding) {
            // 更新绑定信息
            el._permissionBinding = binding;
            // 重新检查权限
            this.checkPermission(el, binding);
        },

        unbind(el) {
            // 清理事件监听器
            if (el._permissionChangeHandler) {
                window.removeEventListener('permission:changed', el._permissionChangeHandler);
                delete el._permissionChangeHandler;
            }
            delete el._permissionBinding;
        },

        checkPermission(el, binding) {
            const { value, modifiers } = binding;

            if (!value) {
                console.warn('v-permission directive requires a value');
                return;
            }

            let hasPermission = false;

            if (typeof value === 'string') {
                // 简单权限检查
                if (modifiers.route) {
                    hasPermission = permissionManager.checkRoutePermission(value);
                } else if (modifiers.component) {
                    hasPermission = permissionManager.checkComponentPermission(value);
                } else if (modifiers.feature) {
                    hasPermission = permissionManager.checkFeaturePermission(value);
                } else if (modifiers.region) {
                    hasPermission = permissionManager.checkRegionPermission(value);
                } else {
                    // 默认检查通用权限（包括区域权限）
                    hasPermission = permissionManager.checkPermission(value);
                }
            } else if (typeof value === 'object') {
                // 复杂权限检查
                const { type, permission, action, context, regionPermissionKey, featurePermissionKey } = value;

                // 如果提供了regionPermissionKey或featurePermissionKey，使用通用权限检查
                if (regionPermissionKey || featurePermissionKey) {
                    hasPermission = permissionManager.checkPermission({
                        regionPermissionKey,
                        featurePermissionKey
                    }, context || {});
                } else {
                    // 原有的类型化权限检查
                    switch (type) {
                    case 'route':
                        hasPermission = permissionManager.checkRoutePermission(permission, context);
                        break;
                    case 'component':
                        hasPermission = permissionManager.checkComponentPermission(permission, action, context);
                        break;
                    case 'feature':
                        hasPermission = permissionManager.checkFeaturePermission(permission, action, context);
                        break;
                    case 'api':
                        hasPermission = permissionManager.checkApiPermission(permission, action, context);
                        break;
                    case 'data':
                        hasPermission = permissionManager.checkDataPermission(permission, action, context);
                        break;
                    default:
                        hasPermission = permissionManager.checkFeaturePermission(permission, action, context);
                    }
                }
            }

            // 根据权限结果处理元素
            if (hasPermission) {
                // 有权限，恢复元素状态
                if (modifiers.hide) {
                    el.style.display = el._originalDisplay || '';
                } else if (modifiers.disable) {
                    el.disabled = el._originalDisabled || false;
                    el.className = el._originalClass || '';
                } else {
                    // 如果元素被移除了，需要重新添加（这种情况比较复杂，建议使用hide模式）
                    if (!el.parentNode && el._parentNode) {
                        el._parentNode.appendChild(el);
                    }
                }
            } else {
                // 没有权限，处理元素
                if (modifiers.hide) {
                    el.style.display = 'none';
                } else if (modifiers.disable) {
                    el.disabled = true;
                    el.classList.add('disabled');
                } else {
                    // 移除元素（保存父节点引用以便恢复）
                    if (el.parentNode) {
                        el._parentNode = el.parentNode;
                        el.parentNode.removeChild(el);
                    }
                }
            }
        }
    });

    // 如果提供了初始化选项，自动初始化
    if (options.autoInit && options.userInfo) {
        permissionManager.initialize(options.userInfo, options.config || {});
    }

    // 将权限混入对象添加到Vue实例，供组件手动使用
    Vue.prototype.$permissionMixin = permissionMixin;
}

// 自动安装（如果在浏览器环境中且Vue可用）
if (typeof window !== 'undefined' && window.Vue) {
    install(window.Vue);
}

// 在install函数外部声明permissionMixin变量，以便导出
let permissionMixin;

// 重新运行install函数以获取permissionMixin
if (typeof window !== 'undefined' && window.Vue) {
    // 从Vue原型中获取permissionMixin
    permissionMixin = window.Vue.prototype.$permissionMixin;
}

// 导出权限混入对象
export { permissionMixin };
