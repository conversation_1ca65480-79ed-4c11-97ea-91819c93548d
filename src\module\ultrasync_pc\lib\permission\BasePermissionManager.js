import { USER_ROLE } from '../constants.js';

/**
 * 权限管理器基类
 * 定义权限管理器的通用接口和方法
 */
class BasePermissionManager {
    constructor() {
        this.permissions = new Map(); // 权限缓存
        this.userInfo = null; // 当前用户信息
        this.initialized = false; // 初始化状态
    }

    /**
     * 初始化权限管理器
     * @param {Object} userInfo - 用户信息
     * @param {Object} config - 配置信息
     */
    async initialize(userInfo, config = {}) {
        this.userInfo = userInfo;
        this.config = config;
        this.initialized = true;
        await this.loadPermissions();
    }

    /**
     * 加载权限数据 - 子类需要实现
     */
    async loadPermissions() {
        throw new Error('loadPermissions method must be implemented by subclass');
    }

    /**
     * 检查权限 - 子类需要实现
     * @param {string} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    hasPermission(permission, context = {}) {
        throw new Error('hasPermission method must be implemented by subclass');
    }

    /**
     * 检查多个权限（AND逻辑）
     * @param {Array<string>} permissions - 权限标识数组
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否都有权限
     */
    hasAllPermissions(permissions, context = {}) {
        return permissions.every(permission => this.hasPermission(permission, context));
    }

    /**
     * 检查多个权限（OR逻辑）
     * @param {Array<string>} permissions - 权限标识数组
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有任一权限
     */
    hasAnyPermission(permissions, context = {}) {
        return permissions.some(permission => this.hasPermission(permission, context));
    }

    /**
     * 获取用户角色
     * @returns {number|string} 用户角色
     */
    getUserRole() {
        return this.userInfo?.role || 0;
    }

    /**
     * 获取用户ID
     * @returns {string|number} 用户ID
     */
    getUserId() {
        return this.userInfo?.uid || this.userInfo?.id;
    }

    /**
     * 获取用户类型
     * @returns {number} 用户类型
     */
    getUserType() {
        return this.userInfo?.type || 1;
    }

    /**
     * 检查是否为管理员
     * @returns {boolean} 是否为管理员
     */
    isAdmin() {
        const role = this.getUserRole();
        return role === USER_ROLE.ADMIN ||
               role === USER_ROLE.SUPER_ADMIN ||
               role === USER_ROLE.DIRECTOR; // 管理员、超级管理员、主任
    }

    /**
     * 检查是否为超级管理员
     * @returns {boolean} 是否为超级管理员
     */
    isSuperAdmin() {
        return this.getUserRole() === USER_ROLE.SUPER_ADMIN; // 超级管理员
    }

    /**
     * 更新用户信息
     * @param {Object} userInfo - 新的用户信息
     */
    updateUserInfo(userInfo) {
        this.userInfo = { ...this.userInfo, ...userInfo };
        this.clearCache();
        this.loadPermissions();

        // 子权限管理器不应该触发权限变化事件，只有主权限管理器才触发
        // 这里不触发事件，由主权限管理器统一处理
    }

    /**
     * 触发权限变化事件
     * @param {Object} changeInfo - 变化信息
     */
    emitPermissionChange(changeInfo) {
        // 防重复触发机制
        const changeKey = `${changeInfo.oldRole}->${changeInfo.newRole}-${changeInfo.oldUserId}->${changeInfo.newUserId}`;
        const now = Date.now();

        // 如果在100ms内有相同的变更，则忽略
        if (this._lastChangeKey === changeKey && (now - this._lastChangeTime) < 100) {
            console.log('[Permission] Duplicate permission change ignored:', changeKey);
            return;
        }

        this._lastChangeKey = changeKey;
        this._lastChangeTime = now;

        // 触发浏览器事件
        if (typeof window !== 'undefined') {
            const event = new CustomEvent('permission:changed', {
                detail: changeInfo
            });
            window.dispatchEvent(event);
        }

        // 触发Vue事件总线（如果存在）
        if (typeof window !== 'undefined' && window.vm) {
            if (window.vm.$emit) {
                window.vm.$emit('permission:changed', changeInfo);
            }
            if (window.vm.$root && window.vm.$root.eventBus && window.vm.$root.eventBus.$emit) {
                window.vm.$root.eventBus.$emit('permission:changed', changeInfo);
            }
        }

        // 记录日志
        console.log('[Permission] Permission changed:', changeInfo);
    }

    /**
     * 清除权限缓存
     */
    clearCache() {
        this.permissions.clear();
    }

    /**
     * 设置权限缓存
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     */
    setCache(key, value) {
        this.permissions.set(key, value);
    }

    /**
     * 获取权限缓存
     * @param {string} key - 缓存键
     * @returns {any} 缓存值
     */
    getCache(key) {
        return this.permissions.get(key);
    }

    /**
     * 检查是否已初始化
     * @returns {boolean} 是否已初始化
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * 销毁权限管理器
     */
    destroy() {
        this.permissions.clear();
        this.userInfo = null;
        this.initialized = false;
    }

    /**
     * 获取权限错误信息
     * @param {string} permission - 权限标识
     * @returns {string} 错误信息
     */
    getPermissionErrorMessage(permission) {
        return `您没有权限执行此操作: ${permission}`;
    }

    /**
     * 记录权限检查日志
     * @param {string} permission - 权限标识
     * @param {boolean} result - 检查结果
     * @param {Object} context - 上下文信息
     */
    logPermissionCheck(permission, result, context = {}) {
        if (window.console && window.console.debug) {
            console.debug(`[Permission Check] ${permission}: ${result}`, {
                userId: this.getUserId(),
                role: this.getUserRole(),
                context
            });
        }
    }
}

export default BasePermissionManager;
